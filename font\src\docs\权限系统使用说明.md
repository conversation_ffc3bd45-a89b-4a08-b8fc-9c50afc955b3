# 权限系统使用说明

## 📋 概述

基于 roles 的权限管理系统，支持两种用户角色：**普通用户** 和 **VIP用户**。

## 🏗️ 架构设计

### 数据结构

```typescript
// 用户信息接口
interface Info {
  id: string;
  name: string;
  username?: string;
  roles?: string[]; // 用户角色数组
  menus: string[];
  vip: {
    isVip: boolean;
    endTime: string;
  };
}

// Store 状态
{
  userInfo: Info;
  lastRefreshTime: number;
  isInitialized: boolean;
}
```

## 🎯 核心功能

### 1. 用户状态管理 (useUserStore)

```typescript
import { useUserStore } from "@/store/modules/user";

const userStore = useUserStore();

// 基本状态
userStore.isLoggedIn; // 是否已登录
userStore.roles; // 用户角色数组
userStore.isVip; // 是否是VIP用户
userStore.isNormalUser; // 是否是普通用户
userStore.userInfo; // 完整用户信息

// 权限判断
userStore.hasRole("VIP用户"); // 是否有指定角色
userStore.hasAnyRole(["普通用户", "VIP用户"]); // 是否有任意角色
userStore.hasAllRoles(["普通用户"]); // 是否有所有角色

// 操作方法
await userStore.refreshUserInfo(); // 刷新用户信息
await userStore.ensureUserInfo(); // 确保用户信息可用
userStore.logout(); // 登出
```

### 2. 权限组合函数 (useAuth)

```typescript
import { useAuth } from "@/composables/useAuth";

const {
  isLoggedIn,
  userInfo,
  roles,
  isVip,
  isNormalUser,
  hasRole,
  hasAnyRole,
  hasAllRoles,
  logout,
  refreshAuth
} = useAuth();
```

## 🎪 使用示例

### 1. 在组件中使用权限控制

```vue
<template>
  <div>
    <!-- 基于角色显示内容 -->
    <button v-if="hasRole('VIP用户')">VIP专属功能</button>
    <button v-if="hasRole('普通用户')">普通用户功能</button>

    <!-- 基于登录状态 -->
    <div v-if="isLoggedIn">欢迎，{{ userInfo.username }}</div>

    <!-- VIP专区 -->
    <div v-if="isVip">VIP专区</div>

    <!-- 普通用户区域 -->
    <div v-if="isNormalUser">普通用户区域</div>
  </div>
</template>

<script setup>
import { useAuth } from "@/composables/useAuth";

const { isLoggedIn, userInfo, isVip, isNormalUser, hasRole, hasAnyRole } =
  useAuth();

// 在方法中使用权限判断
const handleVipFeature = () => {
  if (!hasRole("VIP用户")) {
    alert("此功能仅限VIP用户使用");
    return;
  }
  // 执行VIP功能
};
</script>
```

### 2. 路由权限控制

```typescript
// 路由配置
const routes = [
  {
    path: "/admin",
    component: AdminView,
    meta: {
      requiresAuth: true,
      roles: ["管理员"]
    }
  },
  {
    path: "/edit",
    component: EditView,
    meta: {
      requiresAuth: true,
      roles: ["编辑员", "管理员"]
    }
  }
];

// 在 main.ts 中设置路由守卫
import { setupRouterGuards } from "@/router/guards";
setupRouterGuards(router);
```

### 3. 应用初始化

```typescript
// main.ts
import { initAuth } from "@/utils/auth";

const app = createApp(App);

// 应用启动时初始化权限
initAuth().then(() => {
  app.mount("#app");
});
```

## 🔄 数据流程

### 登录流程

1. 用户输入账号密码
2. 调用登录API获取token
3. 保存token到localStorage
4. 调用info API获取用户信息（包含roles）
5. 保存用户信息到store
6. 跳转到首页

### 页面刷新流程

1. 检查localStorage中的token
2. 如果有token，调用ensureUserInfo()
3. 检查用户信息是否新鲜（5分钟内）
4. 如果不新鲜，重新调用info API
5. 更新store中的用户信息

### 路由守卫流程

1. 检查是否为公共路由
2. 检查token是否存在
3. 确保用户信息可用
4. 检查路由所需的角色权限
5. 权限通过则放行，否则跳转403

## ⚙️ 配置选项

### 缓存时间配置

```typescript
// store/modules/user.ts
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟
```

### 角色映射配置

```typescript
// 可以根据需要扩展角色判断逻辑
const isAdmin = computed(() => {
  const userRoles = state.userInfo.roles || [];
  return userRoles.includes("管理员") || userRoles.includes("admin");
});
```

## 🚨 注意事项

1. **前端权限只是用户体验**：真正的权限验证必须在后端
2. **Token过期处理**：系统会自动检测token过期并清除本地数据
3. **数据持久化**：用户信息会持久化到localStorage，但会定期刷新
4. **权限更新**：用户权限变更后需要重新登录或刷新用户信息

## 🔧 扩展指南

### 添加新的权限判断

```typescript
// store/modules/user.ts getters 中添加
canEditPost: state => {
  const userRoles = state.userInfo.roles || [];
  return userRoles.includes("编辑员") || userRoles.includes("管理员");
};
```

### 添加菜单权限控制

```typescript
// 基于menus字段的权限控制
hasMenu: state => {
  return (menuId: string) => {
    const menus = state.userInfo.menus || [];
    return menus.includes(menuId);
  };
};
```
