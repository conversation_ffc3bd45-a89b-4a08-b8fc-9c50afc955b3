import { useUserStore } from '@/store/modules/user'
import { useRouter } from 'vue-router'

/**
 * 应用初始化时的权限检查
 */
export async function initAuth() {
  const userStore = useUserStore()
  const token = localStorage.getItem('Authorization')
  
  if (token) {
    try {
      // 确保用户信息可用
      const success = await userStore.ensureUserInfo()
      if (success) {
        console.log('用户信息初始化成功')
        console.log('用户角色:', userStore.roles)
        return true
      }
    } catch (error) {
      console.error('用户信息初始化失败:', error)
    }
  }
  
  return false
}

/**
 * 路由权限检查
 */
export function checkRouteAuth(to: any) {
  const userStore = useUserStore()
  
  // 不需要认证的路由
  const publicRoutes = ['/login', '/register']
  if (publicRoutes.includes(to.path)) {
    return true
  }
  
  // 检查是否已登录
  if (!userStore.isLoggedIn) {
    return false
  }
  
  // 检查角色权限
  if (to.meta?.roles) {
    return userStore.hasAnyRole(to.meta.roles)
  }
  
  return true
}

/**
 * 清除认证信息
 */
export function clearAuth() {
  const userStore = useUserStore()
  userStore.clearUserInfo()
}
