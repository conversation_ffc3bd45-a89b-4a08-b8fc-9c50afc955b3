import { defineStore } from "pinia";
import type { Info } from "@/views/user/user";
import api from "@/api/index";

export const useUserStore = defineStore("phone-user-info", {
  state: () => ({
    userInfo: {} as Info,
    lastRefreshTime: 0, // 上次刷新时间
    isInitialized: false // 是否已初始化
  }),

  getters: {
    // 是否已登录
    isLoggedIn: state => {
      return (
        !!state.userInfo.username && !!localStorage.getItem("Authorization")
      );
    },

    // 获取用户角色
    roles: state => {
      return state.userInfo.roles || [];
    },

    // 判断是否有指定角色
    hasRole: state => {
      return (role: string) => {
        const userRoles = state.userInfo.roles || [];
        return userRoles.includes(role);
      };
    },

    // 判断是否有任意一个指定角色
    hasAnyRole: state => {
      return (roles: string[]) => {
        const userRoles = state.userInfo.roles || [];
        return roles.some(role => userRoles.includes(role));
      };
    },

    // 判断是否有所有指定角色
    hasAllRoles: state => {
      return (roles: string[]) => {
        const userRoles = state.userInfo.roles || [];
        return roles.every(role => userRoles.includes(role));
      };
    },

    // 是否是VIP用户
    isVip: state => {
      const userRoles = state.userInfo.roles || [];
      return userRoles.includes("VIP用户");
    },

    // 是否是普通用户
    isNormalUser: state => {
      const userRoles = state.userInfo.roles || [];
      return userRoles.includes("普通用户");
    },

    // 检查用户信息是否新鲜（5分钟内）
    isUserInfoFresh: state => {
      const now = Date.now();
      const CACHE_DURATION = 5 * 60 * 1000; // 5分钟
      return now - state.lastRefreshTime < CACHE_DURATION;
    },

    // 是否有基本用户信息
    hasBasicUserInfo: state => {
      return (
        !!state.userInfo.username && (state.userInfo.roles?.length || 0) > 0
      );
    }
  },

  actions: {
    setUserInfo(data: Info) {
      this.userInfo = {
        ...data,
        vip: data.vip || {
          isVip: false,
          endTime: ""
        }
      };
      this.lastRefreshTime = Date.now();
      this.isInitialized = true;
    },

    // 刷新用户信息
    async refreshUserInfo(force = false) {
      // 如果数据还新鲜且不是强制刷新，直接返回
      if (!force && this.isUserInfoFresh && this.hasBasicUserInfo) {
        console.log("用户信息仍然新鲜，跳过请求");
        return this.userInfo;
      }

      try {
        console.log("发起用户信息请求...");
        const response = await api.login.info({});
        this.setUserInfo(response);
        return response;
      } catch (error) {
        console.error("获取用户信息失败:", error);
        this.clearUserInfo();
        throw error;
      }
    },

    // 确保用户信息可用
    async ensureUserInfo() {
      const token = localStorage.getItem("Authorization");

      if (!token) {
        this.clearUserInfo();
        return false;
      }

      // 如果没有基本信息或需要刷新，则请求
      if (!this.hasBasicUserInfo || !this.isUserInfoFresh) {
        try {
          await this.refreshUserInfo();
          return true;
        } catch (error) {
          this.clearUserInfo();
          return false;
        }
      }

      return true;
    },

    clearUserInfo() {
      this.userInfo = {} as Info;
      this.lastRefreshTime = 0;
      this.isInitialized = false;
      localStorage.removeItem("Authorization");
    },

    // 登出
    logout() {
      this.clearUserInfo();
      // 可以在这里添加跳转到登录页的逻辑
    }
  },

  persist: {
    // 持久化用户信息，但敏感信息会定期刷新
    paths: ["userInfo", "lastRefreshTime", "isInitialized"]
  }
});
