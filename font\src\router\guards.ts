import type { Router } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { initAuth, checkRouteAuth } from '@/utils/auth'

/**
 * 设置路由守卫
 */
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    console.log('路由守卫: 导航到', to.path)
    
    const userStore = useUserStore()
    
    // 公共路由直接放行
    const publicRoutes = ['/login', '/register']
    if (publicRoutes.includes(to.path)) {
      // 如果已登录用户访问登录页，重定向到首页
      if (userStore.isLoggedIn && to.path === '/login') {
        next('/home')
        return
      }
      next()
      return
    }
    
    // 检查token
    const token = localStorage.getItem('Authorization')
    if (!token) {
      console.log('路由守卫: 无token，跳转登录')
      next('/login')
      return
    }
    
    // 确保用户信息可用
    try {
      const hasValidUser = await userStore.ensureUserInfo()
      if (!hasValidUser) {
        console.log('路由守卫: 用户信息无效，跳转登录')
        next('/login')
        return
      }
    } catch (error) {
      console.error('路由守卫: 获取用户信息失败', error)
      next('/login')
      return
    }
    
    // 检查路由权限
    if (to.meta?.roles) {
      const hasPermission = userStore.hasAnyRole(to.meta.roles as string[])
      if (!hasPermission) {
        console.log('路由守卫: 权限不足，跳转403')
        next('/403')
        return
      }
    }
    
    console.log('路由守卫: 权限检查通过')
    next()
  })
  
  // 全局后置钩子
  router.afterEach((to, from) => {
    console.log('路由守卫: 导航完成', to.path)
    // 可以在这里添加页面标题设置、埋点等逻辑
  })
}

/**
 * 路由元信息类型扩展示例
 */
declare module 'vue-router' {
  interface RouteMeta {
    // 是否需要认证
    requiresAuth?: boolean
    // 需要的角色
    roles?: string[]
    // 页面标题
    title?: string
    // 是否缓存
    keepAlive?: boolean
  }
}
