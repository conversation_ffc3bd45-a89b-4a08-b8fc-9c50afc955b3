import { computed } from "vue";
import { useUserStore } from "@/store/modules/user";
import { useRouter } from "vue-router";

/**
 * 权限相关的组合式函数
 */
export function useAuth() {
  const userStore = useUserStore();
  const router = useRouter();

  // 基本状态
  const isLoggedIn = computed(() => userStore.isLoggedIn);
  const userInfo = computed(() => userStore.userInfo);
  const roles = computed(() => userStore.roles);
  const isVip = computed(() => userStore.isVip);
  const isNormalUser = computed(() => userStore.isNormalUser);

  // 权限判断函数
  const hasRole = (role: string) => userStore.hasRole(role);
  const hasAnyRole = (roles: string[]) => userStore.hasAnyRole(roles);
  const hasAllRoles = (roles: string[]) => userStore.hasAllRoles(roles);

  // 登出函数
  const logout = async () => {
    userStore.logout();
    await router.push("/login");
  };

  // 确保用户信息可用
  const ensureAuth = async () => {
    return await userStore.ensureUserInfo();
  };

  // 强制刷新用户信息
  const refreshAuth = async () => {
    return await userStore.refreshUserInfo(true);
  };

  return {
    // 状态
    isLoggedIn,
    userInfo,
    roles,
    isVip,
    isNormalUser,

    // 方法
    hasRole,
    hasAnyRole,
    hasAllRoles,
    logout,
    ensureAuth,
    refreshAuth
  };
}

/**
 * 权限指令相关的工具函数
 */
export const authUtils = {
  // 检查路由权限
  checkRoutePermission: (route: any, userStore: any) => {
    if (!route.meta?.roles) return true;

    const requiredRoles = route.meta.roles;
    return userStore.hasAnyRole(requiredRoles);
  },

  // 检查菜单权限
  checkMenuPermission: (menuId: string, userStore: any) => {
    const menus = userStore.userInfo.menus || [];
    return menus.includes(menuId);
  }
};
