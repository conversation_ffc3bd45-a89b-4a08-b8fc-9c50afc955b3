# 权限使用示例

## 🎯 针对您的两个角色：普通用户 和 VIP用户

### 📝 在组件中使用

```vue
<template>
  <div>
    <!-- 用户信息显示 -->
    <div v-if="isLoggedIn" class="user-info">
      <p>欢迎，{{ userInfo.username }}</p>
      <p>当前角色：{{ roles.join(', ') }}</p>
    </div>

    <!-- 基于角色的功能显示 -->
    <div class="features">
      <!-- 普通用户功能 -->
      <button v-if="hasRole('普通用户')" class="btn">
        普通功能
      </button>
      
      <!-- VIP专属功能 -->
      <button v-if="hasRole('VIP用户')" class="btn vip">
        VIP专属功能
      </button>
      
      <!-- 使用便捷的getter -->
      <div v-if="isVip" class="vip-section">
        <h3>VIP专区</h3>
        <p>享受VIP特权服务</p>
      </div>
      
      <div v-if="isNormalUser" class="normal-section">
        <h3>普通用户区域</h3>
        <p>基础功能服务</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth'

const {
  isLoggedIn,
  userInfo,
  roles,
  isVip,
  isNormalUser,
  hasRole
} = useAuth()

// 示例：VIP功能处理
const handleVipFeature = () => {
  if (!isVip) {
    alert('此功能需要VIP权限')
    return
  }
  // 执行VIP功能
  console.log('执行VIP功能')
}

// 示例：检查权限后执行操作
const handlePremiumAction = () => {
  if (hasRole('VIP用户')) {
    // VIP用户逻辑
    console.log('VIP用户操作')
  } else if (hasRole('普通用户')) {
    // 普通用户逻辑
    console.log('普通用户操作')
  } else {
    alert('请先登录')
  }
}
</script>
```

### 🛡️ 路由权限配置

```typescript
// router配置示例
const routes = [
  {
    path: '/vip',
    component: VipView,
    meta: {
      requiresAuth: true,
      roles: ['VIP用户']
    }
  },
  {
    path: '/normal',
    component: NormalView,
    meta: {
      requiresAuth: true,
      roles: ['普通用户', 'VIP用户'] // 两种角色都可以访问
    }
  }
]
```

### 🎪 常用权限判断模式

```javascript
// 1. 简单角色判断
if (userStore.isVip) {
  // VIP用户逻辑
}

if (userStore.isNormalUser) {
  // 普通用户逻辑
}

// 2. 具体角色检查
if (userStore.hasRole('VIP用户')) {
  // VIP功能
}

// 3. 多角色检查
if (userStore.hasAnyRole(['普通用户', 'VIP用户'])) {
  // 任意角色都可以访问的功能
}

// 4. 在方法中使用
const { isVip, hasRole } = useAuth()

const someAction = () => {
  if (!isVip) {
    showUpgradeDialog() // 显示升级VIP对话框
    return
  }
  // 执行VIP功能
}
```

### 💡 实际业务场景

```vue
<template>
  <div class="app">
    <!-- 导航栏 -->
    <nav>
      <router-link to="/home">首页</router-link>
      <router-link v-if="isLoggedIn" to="/profile">个人中心</router-link>
      <router-link v-if="isVip" to="/vip">VIP专区</router-link>
    </nav>

    <!-- 功能区域 -->
    <main>
      <!-- 普通用户看到的内容 -->
      <div v-if="isNormalUser">
        <h2>普通用户功能</h2>
        <button @click="upgradeToVip">升级VIP</button>
      </div>

      <!-- VIP用户看到的内容 -->
      <div v-if="isVip">
        <h2>VIP专享功能</h2>
        <button @click="vipFeature1">高级功能1</button>
        <button @click="vipFeature2">高级功能2</button>
      </div>
    </main>
  </div>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth'

const { isLoggedIn, isVip, isNormalUser } = useAuth()

const upgradeToVip = () => {
  // 跳转到VIP升级页面
  router.push('/upgrade')
}

const vipFeature1 = () => {
  // VIP功能1
}

const vipFeature2 = () => {
  // VIP功能2
}
</script>
```

## 🔑 关键要点

1. **使用 `isVip` 和 `isNormalUser`** - 最简洁的权限判断
2. **使用 `hasRole()`** - 当需要精确角色匹配时
3. **组件级权限控制** - 通过 `v-if` 控制显示
4. **方法级权限控制** - 在函数中检查权限
5. **路由级权限控制** - 通过路由守卫控制访问
