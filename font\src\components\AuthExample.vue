<template>
  <div class="auth-example">
    <h3>权限控制示例</h3>
    
    <!-- 基本用户信息 -->
    <div class="user-info">
      <p>用户名: {{ userInfo.username || '未登录' }}</p>
      <p>角色: {{ roles.join(', ') || '无角色' }}</p>
      <p>登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}</p>
    </div>
    
    <!-- 基于角色的权限控制 -->
    <div class="role-controls">
      <h4>角色权限控制</h4>
      
      <!-- 普通用户可见 -->
      <button v-if="hasRole('普通用户')" class="btn">
        普通用户功能
      </button>
      
      <!-- 管理员可见 -->
      <button v-if="isAdmin" class="btn admin">
        管理员功能
      </button>
      
      <!-- 多角色权限 -->
      <button v-if="hasAnyRole(['编辑员', '管理员'])" class="btn">
        编辑功能
      </button>
      
      <!-- 需要所有角色 -->
      <button v-if="hasAllRoles(['普通用户', '会员'])" class="btn">
        会员专属功能
      </button>
    </div>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <button @click="refreshAuth" class="btn">刷新用户信息</button>
      <button @click="logout" class="btn danger">登出</button>
    </div>
    
    <!-- 调试信息 -->
    <div class="debug-info">
      <h4>调试信息</h4>
      <pre>{{ JSON.stringify(userInfo, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'

// 使用权限组合函数
const {
  isLoggedIn,
  userInfo,
  roles,
  isAdmin,
  hasRole,
  hasAnyRole,
  hasAllRoles,
  logout,
  refreshAuth
} = useAuth()

// 示例：在方法中使用权限判断
const handleSomeAction = () => {
  if (!hasRole('管理员')) {
    alert('您没有权限执行此操作')
    return
  }
  
  // 执行管理员操作
  console.log('执行管理员操作')
}
</script>

<style scoped>
.auth-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.user-info, .role-controls, .actions, .debug-info {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.btn {
  margin: 5px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.btn.admin {
  background-color: #dc3545;
}

.btn.danger {
  background-color: #dc3545;
}

.btn:hover {
  opacity: 0.8;
}

.debug-info pre {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

h3, h4 {
  margin-top: 0;
  color: #333;
}
</style>
